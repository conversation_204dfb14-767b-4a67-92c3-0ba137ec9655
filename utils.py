from qdrant_client import Qdrant<PERSON>lient
from sentence_transformers import SentenceTransformer
from qdrant_client.models import Distance, VectorParams, PointStruct

model = None
qdrant_client = None


def get_qdrant():
    global qdrant_client
    if not qdrant_client:
        qdrant_client = QdrantClient(url="http://localhost:6333", api_key="None")
    return qdrant_client


def get_embedding_model() -> SentenceTransformer:
    global model
    if not model:
        model = SentenceTransformer("all-MiniLM-L6-v2")
    return model


def encode_stories(stories: list[str]):
    model = get_embedding_model()
    return model.encode(stories)


def _ensure_collection_exists(collection_name: str):
    qdrant_client = get_qdrant()
    try:
        qdrant_client.get_collection(collection_name=collection_name)
    except Exception as e:
        dimension = get_embedding_model().get_sentence_embedding_dimension()
        qdrant_client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(distance=Distance.COSINE, size=int(dimension)))


def ingest_stories(texts: list[str], collection_name: str):
    model = get_embedding_model()
    array_vectors = model.encode(texts, normalize_embeddings=True)

    points = []
    for i in range(len(texts)):
        point = PointStruct(id=i + 1,
                            vector=array_vectors[i].tolist(),
                            payload={"text": texts[i]})
        points.append(point)

    qdrant = get_qdrant()
    _ensure_collection_exists(collection_name)
    qdrant.upsert(collection_name=collection_name, points=points)


def get_similarity_search(collection_name:str):
    def similarity_search(query:str) -> list[str]:
        """
        :param query: Conduct a similarity search in the qdrant database and get relevant result
        :return: list of texts which match the query
        """
        vector_query = get_embedding_model().encode(query)
        qdrant_client = get_qdrant()
        results = qdrant_client.search(collection_name=collection_name, query_vector=vector_query, limit=3)

        result_texts = []
        for r in results:
            result_texts.append(r.payload["text"])

        return result_texts
    return similarity_search

