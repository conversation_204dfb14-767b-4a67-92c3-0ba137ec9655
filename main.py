import os
from contextlib import asynccontextmanager
from typing import List, Dict, Optional
from agno.storage.sqlite import SqliteStorage
from agno.agent import Agent
from agno.models.google import Gemini
from langchain_community.llms.aviary import get_models
from pydantic import BaseModel, Field
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from dotenv import load_dotenv
from utils import get_qdrant, get_embedding_model, get_similarity_search, ingest_stories
from fastapi import FastAPI
import os
load_dotenv()

COLLECTION_NAME = "testing"

import os
path_to_dir = os.getcwd()
path_to_Stories = os.path.join(path_to_dir, "stories")
list_stories = os.listdir(path_to_Stories)
stories = []
for f in list_stories:
    file_path = os.path.join(path_to_dir, "stories", f)
    with open(file_path) as f:
        stories.append(f.read())

@asynccontextmanager
async def lifespan(app: FastAPI):
    get_qdrant()
    get_embedding_model()
    ingest_stories(collection_name=COLLECTION_NAME, texts=stories)
    yield

app = FastAPI(lifespan = lifespan)
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class UserMessage(BaseModel):
    conversation_id: Optional[str] = Field(None, description="conversation_id")
    message: str = Field(..., description="User message is needed", min_length=4)

@app.post("/conversation")
async def converse(message: UserMessage):
    import uuid
    if not message.conversation_id:
        message.conversation_id = str(uuid.uuid4())
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
    agent = Agent(model=Gemini(id="gemini-2.0-flash",api_key=GOOGLE_API_KEY),
                  show_tool_calls=True,
                  telemetry=False,
                  debug_mode=True,
                  session_id=message.conversation_id,
                  instructions="""You are a helpful chat bot assistant. You will never reply from your knowledge. 
                  Please use the tools available only to answer the question from the user.
                  You are given a tool to query the VectorDB and get the list of texts which match the query. 
                  Please use the tool to answer the users questions.
                  """,
                  markdown=True,
                  tools=[get_similarity_search(COLLECTION_NAME)]
                  )

    response = agent.run(message.message)

    if response.content:
        return {"response":response.content, "conversation_id": message.conversation_id}
    else:
        return {"response":response, "conversation_id": message.conversation_id}




if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port= 8000)
