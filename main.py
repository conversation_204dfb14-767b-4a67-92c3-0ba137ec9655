# Import necessary libraries and modules
import os
from contextlib import asynccontextmanager
from typing import List, Dict, Optional

# Agno framework imports for AI agent functionality
from agno.storage.sqlite import SqliteStorage
from agno.agent import Agent
from agno.models.google import Gemini

# LangChain and ML model imports
from langchain_community.llms.aviary import get_models
from pydantic import BaseModel, Field
from sentence_transformers import SentenceTransformer

# Qdrant vector database imports
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct

# Environment and utility imports
from dotenv import load_dotenv
from utils import get_qdrant, get_embedding_model, get_similarity_search, ingest_stories

# FastAPI web framework imports
from fastapi import FastAPI
import os

# Load environment variables from .env file
load_dotenv()

# Configuration: Name of the Qdrant collection to store story embeddings
COLLECTION_NAME = "testing"

# Story loading section: Read all story files from the 'stories' directory
import os
path_to_dir = os.getcwd()  # Get current working directory
path_to_Stories = os.path.join(path_to_dir, "stories")  # Path to stories folder
list_stories = os.listdir(path_to_Stories)  # List all files in stories directory
stories = []  # Initialize empty list to store story contents

# Loop through each file in the stories directory and read its content
for f in list_stories:
    file_path = os.path.join(path_to_dir, "stories", f)  # Full path to story file
    with open(file_path) as f:  # Open and read file content
        stories.append(f.read())  # Add story content to stories list

# FastAPI lifespan context manager: Handles startup and shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Initialize Qdrant client, embedding model, and ingest stories
    get_qdrant()  # Initialize Qdrant vector database client
    get_embedding_model()  # Load the sentence transformer embedding model
    ingest_stories(collection_name=COLLECTION_NAME, texts=stories)  # Store stories in vector DB
    yield  # Application runs here
    # Shutdown: Any cleanup code would go here (currently none needed)

# Create FastAPI application instance with lifespan management
app = FastAPI(lifespan = lifespan)

# CORS middleware setup: Enable cross-origin requests from any domain
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Pydantic model: Defines the structure for incoming user messages
class UserMessage(BaseModel):
    conversation_id: Optional[str] = Field(None, description="conversation_id")  # Optional conversation ID
    message: str = Field(..., description="User message is needed", min_length=4)  # Required user message (min 4 chars)

# Main API endpoint: Handles conversation requests and returns AI agent responses
@app.post("/conversation")
async def converse(message: UserMessage):
    import uuid

    # Generate a new conversation ID if one wasn't provided
    if not message.conversation_id:
        message.conversation_id = str(uuid.uuid4())

    # Get Google API key from environment variables
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

    # Create AI agent with Gemini model and vector search capabilities
    agent = Agent(model=Gemini(id="gemini-2.0-flash",api_key=GOOGLE_API_KEY),
                  show_tool_calls=True,  # Display tool usage for debugging
                  telemetry=False,  # Disable telemetry
                  debug_mode=True,  # Enable debug mode
                  session_id=message.conversation_id,  # Track conversation sessions
                  instructions="""You are a helpful chat bot assistant. You will never reply from your knowledge.
                  Please use the tools available only to answer the question from the user.
                  You are given a tool to query the VectorDB and get the list of texts which match the query.
                  Please use the tool to answer the users questions.
                  """,  # Agent instructions to use only vector search, not pre-trained knowledge
                  markdown=True,  # Enable markdown formatting in responses
                  tools=[get_similarity_search(COLLECTION_NAME)]  # Provide vector search tool
                  )

    # Run the agent with the user's message and get response
    response = agent.run(message.message)

    # Return response with conversation ID, handling both content and non-content responses
    if response.content:
        return {"response":response.content, "conversation_id": message.conversation_id}
    else:
        return {"response":response, "conversation_id": message.conversation_id}




if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port= 8000)
