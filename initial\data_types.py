age = 10



# print("this is a string "+str(age))

# null and undefined --- python None
x = None

variable = ""

# None is converted to False in if statement
# for comparison with None or False or True we use "is" operator
if not variable:
    print("Empty string is False")
else:
    print("Empty string is not False")


# def calculate_emi(param1, param2, param10):
#     print("Calculating")
# calculate_emi(10,20,30) #positional calling
# calculate_emi(param1=10, param10=2, param2=23) #keyword argument

strings = "My age is 34"

#  in operator
if "age" in strings:
    print("I have an age")

list_1 = [1,2,3,4]
list_1.append("asdfsd")
# print(list_1 + [3,45,6])

def all_nums_list(list):
    sum = 0
    for num in list:
        if type(num) == int:
            sum += num
    return sum
if 10 in list_1:
    list_1.remove(10)

def get_tuple():
    return 1,2,3

# print(type(get_tuple()))

person_dict = {"name": "Abhijeet"}

# print(person_dict.get("name1", "Name not provided"))

person_dict["name"] = "some"

del person_dict["name"]

sett = {1,2,3,4,5,1}
print(sett)
sett.remove(1)
sett.discard(1)
print(sett)
list_1 = [1,2,3434,34,2,4]
list_1.pop(0)
print(list_1)

print(float(10))