from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

load_dotenv()

import os
openai_api_key = os.getenv("OPENAI_API_KEY")

story = """It was a rainy Thursday afternoon when <PERSON><PERSON>’s train came to an unexpected halt in the middle of a forested stretch. The loudspeaker crackled to life, announcing that a fallen tree had blocked the tracks ahead. As other passengers sighed in frustration, <PERSON><PERSON> noticed an elderly man sitting quietly by the window, sketching the view outside. His hands trembled slightly, yet each line he drew seemed precise and full of intention. Curiosity got the better of her, and she asked what he was drawing. He smiled and said, “I’m finishing a picture I started forty years ago, at this very spot.”

The man explained that decades earlier, on his honeymoon journey, their train had stopped in the same place for the same reason — a fallen tree. Back then, he had started to sketch the forest while his new wife leaned on his shoulder. But the train had moved before he could finish, and he’d kept the half-drawn page all these years. “She passed away last winter,” he said softly. “I thought I’d never come back here, but life… it surprises you.” <PERSON><PERSON> watched as the man added the last few strokes, completing a drawing four decades in the making.
"""
sys_message = SystemMessage(content=
                            f"""You are a helpful assistant. 
                            You will never respond from your knowledge
                            

""")
human_message = HumanMessage(content="My Name is Abhijeet. I am a genai developer and an architect")
ai_message = AIMessage(content="Hi Abhijeet! How are you doing today?")
human_message_1 = HumanMessage(content="Hey I am fine! What is my name?")

chat_model = ChatOpenAI(model="gpt-4o", temperature=0, openai_api_key=openai_api_key)


messages_array = [sys_message, human_message, ai_message, human_message_1]

response = chat_model.invoke(messages_array)

if response.content:
    print(response.content)
else:
    print(response)
