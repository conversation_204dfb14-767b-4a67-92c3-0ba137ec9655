
class Person:

    def __init__(self, name, age):
        self.name = name
        self.age = age
        self._backlogs = 0

    def __eq__(self, other):
        if isinstance(other, Person):
            if self.age == other.age and self.name == other.name:
                return True
            else:
                return False

    def __lt__(self, other):
        pass

    def __gt__(self, other):
        pass




person = Person(name="<PERSON><PERSON><PERSON><PERSON><PERSON>", age=35)


